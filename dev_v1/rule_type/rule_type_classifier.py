# -*- coding: utf-8 -*-
import sys
import os
import logging

# 添加父目录到路径以导入model_use和config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from model_use import llm_use
from config import qwen_30B_config
model_config = qwen_30B_config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def classify_rule_type(rule_data):
    """
    使用大模型判断规则是内涵质控标准还是规则质控标准
    
    Args:
        rule_data (dict): 包含规则信息的字典
        
    Returns:
        str: "内涵" 或 "规则"
    """
    
    # 构建系统提示词
    system_prompt = """
你是一个医疗质控专家，需要判断给定的质控规则属于哪种类型：

1. 内涵质控标准：主要关注医疗内容的质量、合理性、完整性等，如诊断依据是否充分、治疗方案是否合理、病历内容是否完整等。
2. 规则质控标准：主要关注格式、时效、流程等规范性要求，如签名是否完整、时间是否及时、格式是否规范等。

请仔细分析规则内容，只返回"内涵"或"规则"，不要返回其他内容。
    """
    
    # 构建用户提示词
    user_prompt = f"""
请判断以下质控规则的类型：

规则名称：{rule_data.get('rule_name', '')}
规则类别：{rule_data.get('category', '')}
规则类型：{rule_data.get('rule_type', '')}
描述：{rule_data.get('description', '')}
文档类型：{rule_data.get('document_type', '')}

请判断这是"内涵"质控标准还是"规则"质控标准？
    """
    
    logger.info(f"开始分类规则: {rule_data.get('rule_name', '')}")
    
    try:
        # 调用大模型进行判断
        result = llm_use(system_prompt, user_prompt, model_config)
        
        if result:
            # 清理结果，只保留"内涵"或"规则"
            result = result.strip()
            if "内涵" in result:
                classification = "内涵"
            elif "规则" in result:
                classification = "规则"
            else:
                # 如果模型返回的结果不明确，根据规则类别进行简单判断
                category = rule_data.get('category', '')
                if category in ['缺项', '时效']:
                    classification = "规则"
                else:
                    classification = "内涵"
                logger.warning(f"模型返回结果不明确: {result}，使用默认分类: {classification}")
        else:
            # 如果模型调用失败，使用默认分类逻辑
            category = rule_data.get('category', '')
            if category in ['缺项', '时效']:
                classification = "规则"
            else:
                classification = "内涵"
            logger.error(f"模型调用失败，使用默认分类: {classification}")
            
        logger.info(f"规则分类完成: {rule_data.get('rule_name', '')} -> {classification}")
        return classification
        
    except Exception as e:
        logger.error(f"分类过程中发生错误: {e}")
        # 发生错误时的默认分类逻辑
        category = rule_data.get('category', '')
        if category in ['缺项', '时效']:
            return "规则"
        else:
            return "内涵"

if __name__ == '__main__':
    # 测试用例
    test_rule = {
        "rule_name": "首次病程缺诊断及诊断依据",
        "category": "缺项",
        "rule_type": "段落完整性",
        "description": "",
        "document_type": "Initial Progress Note"
    }
    
    result = classify_rule_type(test_rule)
    print(f"测试结果: {result}")